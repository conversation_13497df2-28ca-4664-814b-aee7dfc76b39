﻿using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Converters;
using shared.Models.Document;
using System.Text.Json.Serialization;

namespace platform.Models.Document
{
    [DynamoDBTable("SignupProcess")]
    public class SignupProcess : BaseModel
    {
        [DynamoDBHashKey]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string Email { get; set; } = string.Empty;

        public string AccountId { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<SignupStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<SignupStatus>))]
        public SignupStatus Status { get; set; }
    }
}
