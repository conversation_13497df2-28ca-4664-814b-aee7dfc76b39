﻿using Amazon.DynamoDBv2.DataModel;
using shared.Models.Document;

namespace platform.Models.Document
{
    [DynamoDBTable("AgentKnowledgeBase")]
    public class AgentKnowledgeBase : BaseModel
    {
        public const string KnowledgebaseIdHashIndex = "KnowledgebaseId-AgentId-index";

        public string AccountId { get; set; } = string.Empty;

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(KnowledgebaseIdHashIndex)]
        public string AgentId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexHashKey(KnowledgebaseIdHashIndex)]
        public string KnowledgebaseId { get; set; } = string.Empty;
    }
}
