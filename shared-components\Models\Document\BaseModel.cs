﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.Model;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace shared.Models.Document
{
    public class BaseModel
    {
        public int DataVersion { get; set; } = 0;
        public int ModelVersion { get; set; } = 0;
        public long LastChangeTimestamp { get; set; } = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        public long CreatedAt { get; set; } = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        public string SearchString { get; set; } = string.Empty;

        public static PropertyInfo? GetPropertyByAttribute(Type T, Type attributeType)
        {
            return T.GetProperties()
            .Select(pi => new { Property = pi, Attribute = pi.GetCustomAttributes(attributeType, true).FirstOrDefault() })
            .Where(x => x.Attribute != null)
            .ToList().First().Property;
        }

        public virtual string GetSearchString() { return string.Empty; }

        public virtual string? GetHashKey()
        {
            var haskeyprop = this.GetType().GetProperties()
            .Select(pi => new { Property = pi, Attribute = pi.GetCustomAttributes(typeof(DynamoDBHashKeyAttribute), true).FirstOrDefault() as DynamoDBHashKeyAttribute })
            .Where(x => x.Attribute != null)
            .ToList().First();

            return (string?)haskeyprop.Property.GetValue(this, null);
        }

        public virtual string? GetRangeKey()
        {
            var haskeyprop = this.GetType().GetProperties()
            .Select(pi => new { Property = pi, Attribute = pi.GetCustomAttributes(typeof(DynamoDBRangeKeyAttribute), true).FirstOrDefault() as DynamoDBRangeKeyAttribute })
            .Where(x => x.Attribute != null)
            .ToList().First();

            return (string?)haskeyprop.Property.GetValue(this, null);
        }

        public Type? GetDynamoDBConverter(string propertyName)
        {
            PropertyInfo? pInfo = GetType().GetProperty(propertyName);
            DynamoDBPropertyAttribute? attr = pInfo?.GetCustomAttribute(typeof(DynamoDBPropertyAttribute)) as DynamoDBPropertyAttribute;
            return attr?.Converter ?? null;
        }

        [DynamoDBIgnore]
        public object this[string propertyName]
        {
            get
            {
                PropertyInfo? pInfo = GetType().GetProperty(propertyName);
                return pInfo.GetValue(this, null);
            }
            set
            {
                PropertyInfo? pInfo = GetType().GetProperty(propertyName);
                pInfo?.SetValue(this, value, null);
            }
        }
    }
}
