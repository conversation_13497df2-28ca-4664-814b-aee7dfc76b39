using shared.Authentication;
using Microsoft.IdentityModel.Tokens;
using Amazon.BedrockAgent;
using Amazon.CognitoIdentityProvider;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2;
using Amazon.IdentityManagement;
using Amazon.S3;
using Amazon.SecretsManager;
using Amazon.SQS;
using shared.Components.APIEvents;
using shared.Services;
using shared.Services.Implementation;
using shared.Models.Configuration;
using platform.Configuration;
using shared.Components.ApiEventBus;
using shared.Components.ApiEventBus.Implementation;
using Microsoft.Extensions.Caching.Memory;
using platform.Components.AWS;
using platform.Services;
using platform.Models.Configuration;
using Microsoft.AspNetCore.Hosting;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();



/*
builder.Services.AddCors(options =>
            {
                options.AddPolicy("LocalhostCORS",
                    builder => builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    );

                options.AddPolicy("signalr",
                    builder => builder
                    .AllowAnyMethod()
                    .AllowAnyHeader()

                    .AllowCredentials()
                    .SetIsOriginAllowed(hostName => true));
            });*/

builder.Services.AddCors(options =>
{
    options.AddPolicy(name: "LocalhostCORS",
                      policy =>
                      {
                          policy.AllowAnyHeader().AllowAnyMethod().AllowCredentials().SetIsOriginAllowed(
                            host => true
                          ); 
                      });
});


builder.Services.Configure<MicroserviceConfiguration>(builder.Configuration.GetSection(nameof(MicroserviceConfiguration)));
builder.Services.Configure<UserPoolConfiguration>(builder.Configuration.GetSection(nameof(UserPoolConfiguration)));
builder.Services.Configure<BedrockConfiguration>(builder.Configuration.GetSection(nameof(BedrockConfiguration)));


builder.Services.AddLogging(loggingBuilder => loggingBuilder
            .AddConsole()
            .AddDebug()
            .SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug));
builder.Services.AddDefaultAWSOptions(builder.Configuration.GetAWSOptions());
builder.Services.AddSingleton<IMemoryCache, MemoryCache>();
builder.Services.AddAWSService<IAmazonDynamoDB>();
builder.Services.AddAWSService<IAmazonBedrockAgent>();
builder.Services.AddAWSService<IAmazonS3>();
builder.Services.AddAWSService<IAmazonSQS>();
builder.Services.AddSingleton<IDynamoDBContext, DynamoDBContext>();
//builder.Services.AddSingleton<IAmazonIdentityManagementService, AmazonIdentityManagementServiceClient>();
builder.Services.AddSingleton<IIAM, AWSIAM>();
builder.Services.AddSingleton<IAmazonCognitoIdentityProvider, AmazonCognitoIdentityProviderClient>();
builder.Services.AddSingleton<IAmazonBedrockAgent, AmazonBedrockAgentClient>();
builder.Services.AddSingleton<IAmazonS3, AmazonS3Client>();
builder.Services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
builder.Services.AddSingleton<IAmazonSecretsManager, AmazonSecretsManagerClient>();
builder.Services.AddSingleton<ISecretsService, AWSSecretsService>();
builder.Services.AddSingleton<IApiEventBus,AWSApiEventBus>();
builder.Services.AddHostedService<IApiEventBus>(provider => provider.GetRequiredService<IApiEventBus>());


builder.Services.AddAuthentication()
    .AddScheme<SecretsJwtBearerOptions, SecretsJwtBearerHandler>(
        shared.Constants.Authentication.MicroserviceAuthScheme,
        x => {
            x.SecretsKeyName = shared.Constants.Authentication.MicroserviceAuthenticationKeySecretsName;
            x.RequireHttpsMetadata = false;
            x.SaveToken = false;
            x.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };
        })
    .AddScheme<SecretsJwtBearerOptions, SecretsJwtBearerHandler>(
        shared.Constants.Authentication.UserAuthScheme,
        x => {
            x.SecretsKeyName = shared.Constants.Authentication.UserAuthenticationKeySecretsName;
            x.RequireHttpsMetadata = false;
            x.SaveToken = false;
            x.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };
        });



var app = builder.Build();


app.MapControllers();
app.UseAuthentication();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseHttpsRedirection();
    app.UseCors("LocalhostCORS");
}


//app.UseHttpsRedirection();


//app.UseAuthorization();

app.Run();
