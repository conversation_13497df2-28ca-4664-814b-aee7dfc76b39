﻿using Amazon.BedrockAgent;
using Amazon.DynamoDBv2.DataModel;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Constants;
using platform.Models.Configuration;
using platform.Models.Document.AWS;
using platform.Models.Request;
using platform.Models.Response;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Components.BehaviorTree;
using shared.Controllers;
using shared.Helpers;
using shared.Models.Document;
using shared.Models.Response;
using System.Diagnostics.Metrics;

namespace platform.Controllers
{
    [Route(Routes.AgentBehaviorTreeController.BasePath)]
    [Produces("application/json")]
    public class AgentBehaviorTreeController : SuperController
    {
        private readonly ILogger<AgentBehaviorTreeController> logger;

        public AgentBehaviorTreeController(ILogger<AgentBehaviorTreeController> logger, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext) : base(apiEventBus, dynamoDBContext)
        {
            this.logger = logger;
        }




        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateBehaviorTree([FromBody] AgentBehaviorTreeCreateRequest request)
        {

            AgentBehaviorTreeDocument behaviorTreeData = new AgentBehaviorTreeDocument();
            behaviorTreeData.Name = request.Name;
            behaviorTreeData.Description = request.Description;
            behaviorTreeData.Id = CUI.Generate(CUI.Resource.BehaviorTree);
            behaviorTreeData.AgentId = request.AgentId;
            behaviorTreeData.AccountId = GetAccountId();

            await PutDBEntry(behaviorTreeData);

            var resp = new CreateBehaviorTreeResponse();
            resp.Id = behaviorTreeData.Id;

            return Ok(resp);

        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Routes.AgentBehaviorTreeController.Public.GET)]
        public async Task<IActionResult> GetBehaviorTree([FromRoute] string btId)
        {
            var accountId = GetAccountId();
            // Use secondary index to query by Id since Id is no longer the range key
            var entries = await GetDBEntriesByGSI<AgentBehaviorTreeDocument>(
                AgentBehaviorTreeDocument.AccountIdIdIndex,
                nameof(AgentBehaviorTreeDocument.AccountId),
                accountId,
                nameof(AgentBehaviorTreeDocument.Id),
                btId,
                1);

            if (entries == null || !entries.Entries.Any()) return NotFound();
            var entry = entries.Entries.First();

            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AgentBehaviorTreeDocument, AgentBehaviorTreeDocumentResponse>();
            }).CreateMapper();

            var resp = mapper.Map<AgentBehaviorTreeDocument, AgentBehaviorTreeDocumentResponse>(entry);
            return Ok(resp);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Routes.AgentBehaviorTreeController.Public.LIST)]
        public async Task<IActionResult> ListBehaviorTrees([FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {

            if (count < 10) count = 10;
            if (count > 100) count = 100;


            var listEntriesInternal = await GetDBEntries<AgentBehaviorTreeDocument>(
                nameof(AgentBehaviorTreeDocument.AccountId),
                GetAccountId(),
                count, nextToken,
                attributesToGet: new List<string>()
                {
                    nameof(AgentBehaviorTreeDocument.CreatedAt),
                    nameof(AgentBehaviorTreeDocument.Description),
                    nameof(AgentBehaviorTreeDocument.Name),
                    nameof(AgentBehaviorTreeDocument.Id),
                    nameof(AgentBehaviorTreeDocument.AgentId),
                    nameof(AgentBehaviorTreeDocument.LastChangeTimestamp)
                },
                getTotal: nextToken == null);

            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AgentBehaviorTreeDocument, AgentBehaviorTreeDocumentResponse>();
            }).CreateMapper();


            var result = new ListResponse<AgentBehaviorTreeDocumentResponse>()
            {
                Entries = mapper.Map<IList<AgentBehaviorTreeDocument>, IList<AgentBehaviorTreeDocumentResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);

        }

    }
}
