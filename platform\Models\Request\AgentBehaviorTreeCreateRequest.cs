﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.BehaviorTree;
using shared.Models.Document;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Models.Document;
using shared.Components.AgentBehaviorTree.Models.Document;

namespace platform.Models.Request
{
    public class AgentBehaviorTreeCreateRequest
    {
        public string AgentId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
