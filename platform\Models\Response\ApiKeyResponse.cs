﻿using Amazon.DynamoDBv2.DataModel;
using shared.Converters;
using shared.Models.Response;
using System.Security.Claims;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class ApiKeyResponse : BaseModelResponse
    {
        public string Name { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string Secret { get; set; } = string.Empty;
        [JsonConverter(typeof(JsonListOfConverter<Claim, JsonClaimConverter>))]
        public List<Claim> Claims { get; set; } = new List<Claim>();
        public string OwnerUserId { get; set; } = string.Empty;
        public long ExpireAt { get; set; } = -1;
    }
}
