﻿using Amazon.CognitoIdentityProvider;
using Amazon.DynamoDBv2.DataModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using platform.Constants;
using shared.Services;
using shared.Components.ApiEventBus;
using platform.Models.Request;
using platform.Components.AWS;
using platform.Models.Document;
using Microsoft.Extensions.Options;
using platform.Configuration;
using platform.Models.Response;
using shared.Models.Enum;
using Amazon.IdentityManagement;
using shared.Constants;
using shared.Models.Configuration;
using Microsoft.Extensions.Caching.Memory;
using platform.Services;


namespace platform.Controllers
{
    [ApiController]
    [Route(Routes.SignupController.BasePath)]
    public class SignupController : shared.Controllers.SuperController
    {

        private readonly IAmazonCognitoIdentityProvider identityProvider;
        private readonly Cognito cognito;
        private readonly IIAM iIAM;

        public SignupController(IApiEventBus eventBus, IDynamoDBContext dBContext, IAmazonCognitoIdentityProvider identityProvider, ISecretsService secretsService, IIAM iIAM, IOptionsMonitor<UserPoolConfiguration> configuration, IOptions<MicroserviceConfiguration> microserviceConfiguration) : base(eventBus, dBContext)
        {
            this.identityProvider = identityProvider;
            this.iIAM = iIAM;

            cognito = new Cognito(identityProvider, microserviceConfiguration, configuration, secretsService);
        }

        [HttpPost]
        public async Task<IActionResult> Signup([FromBody] SignupRequest signupRequest)
        {
            SignupProcess signupProcess = new SignupProcess();
            signupProcess.Status = platform.Models.Enum.SignupStatus.QUEUED;
            signupProcess.Email = signupRequest.Email;
            signupProcess = await PutDBEntry<SignupProcess>(signupProcess);

            SignupInternalRequest signupInternalRequest = new SignupInternalRequest();
            signupInternalRequest.SignupRequest = signupRequest;
            signupInternalRequest.SignupProcess = signupProcess;

            if (signupProcess == null) {
                return StatusCode(500, "Couldn't process your request");
            }

            if (!await DispatchApiEvent(signupInternalRequest,MicroserviceType.Platform, Routes.SignupController.BasePath, Routes.SignupController.Internal.SIGNUP_PROCESS))
            {
                await DeleteDBEntry<SignupProcess>(signupProcess.Id);
                return StatusCode(500, "Couldn't process your request");
            }

            return Ok(new SignupResponse { RequestId = signupProcess.Id, Status = signupProcess.Status});

        }

        [IgnoreAntiforgeryToken]
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [HttpPost(Routes.SignupController.Internal.SIGNUP_PROCESS)]
        public async Task<IActionResult> SignupProcess([FromBody] SignupInternalRequest signupInternalRequest) {

            string userId = Guid.NewGuid().ToString();
            string accountId = Guid.NewGuid().ToString();

            int requeueDelay = 5;
            switch (signupInternalRequest.SignupProcess.Status)
            {
                case platform.Models.Enum.SignupStatus.QUEUED:

                    var userCreateResponse = await cognito.CreateUser(
                        signupInternalRequest.SignupRequest.Email, 
                        signupInternalRequest.SignupRequest.Password, 
                        new Dictionary<string, string> { 
                            { "name", signupInternalRequest.SignupRequest.Name} ,
                            { "email", signupInternalRequest.SignupRequest.Email},
                            { "custom:AccountId", accountId},
                    }, userId);

                    if (userCreateResponse != null)
                    {
                        requeueDelay = 0;
                        signupInternalRequest.SignupProcess.Status = platform.Models.Enum.SignupStatus.USER_CREATED;
                        signupInternalRequest.SignupProcess.UserId = userCreateResponse.UserId;
                        signupInternalRequest.SignupProcess.AccountId = accountId;
                        await PutDBEntry<SignupProcess>(signupInternalRequest.SignupProcess);
                    }
                    else
                    {
                        return StatusCode(500, "Some Error while crating user");
                    }
                    break;
                case platform.Models.Enum.SignupStatus.USER_CREATED:
                    Account account = new Account();
                    account.OrganizationName = signupInternalRequest.SignupRequest.OrganizationName;
                    account.Id = signupInternalRequest.SignupProcess.AccountId;
                    account = await PutDBEntry<Account>(account);
                    if(account != null)
                    {
                        requeueDelay = 0;
                        signupInternalRequest.SignupProcess.Status = platform.Models.Enum.SignupStatus.ACCOUNT_CREATED;
                        signupInternalRequest.SignupProcess.AccountId = account.Id;
                        await PutDBEntry<SignupProcess>(signupInternalRequest.SignupProcess);
                    }
                    else{
                        return StatusCode(500, "Other Error");
                    }
                    break;
                case platform.Models.Enum.SignupStatus.ACCOUNT_CREATED:
                    AccountUser accountUser = new AccountUser();
                    accountUser.AccountId = signupInternalRequest.SignupProcess.AccountId;
                    accountUser.UserId = signupInternalRequest.SignupProcess.UserId;
                    accountUser.Role = platform.Models.Enum.UserRole.Admin;
                    accountUser = await PutDBEntry<AccountUser>(accountUser);
                    if(accountUser != null) 
                    { 
                        if (await cognito.UpdateUserAttributes(signupInternalRequest.SignupProcess.Email, new Dictionary<string, string>() { { "custom:AccountId", signupInternalRequest.SignupProcess.AccountId } }))
                        {
                            requeueDelay = 0;
                            signupInternalRequest.SignupProcess.Status = platform.Models.Enum.SignupStatus.ACCOUNT_ASSIGNED;
                        
                            await PutDBEntry<SignupProcess>(signupInternalRequest.SignupProcess);
                            break;
                        }
                    }
                    return StatusCode(500, "Other Error");
                case platform.Models.Enum.SignupStatus.ACCOUNT_ASSIGNED:
                    if (await platform.Components.Core.Account.SetupAccount(iIAM, signupInternalRequest.SignupProcess.AccountId))
                    {
                        requeueDelay = 5;
                        signupInternalRequest.SignupProcess.Status = platform.Models.Enum.SignupStatus.EMAIL_CONFIRMATION;
                        var latestSignupProcess = await PutDBEntry<SignupProcess>(signupInternalRequest.SignupProcess);
                        if(latestSignupProcess != null) return Ok();
                    }
                    break;
                case platform.Models.Enum.SignupStatus.EMAIL_CONFIRMATION:
                    {
                        requeueDelay = 5;
                        var latestSignupProcess = await PutDBEntry<SignupProcess>(signupInternalRequest.SignupProcess);
                        if (latestSignupProcess != null) return Ok();
                    }
                    break;
                case platform.Models.Enum.SignupStatus.FINISHED:
                    requeueDelay = 5;
                    if (await DeleteDBEntry<SignupProcess>(signupInternalRequest.SignupProcess.Id, true))
                        return Ok();
                    break;

            }
            if(await DispatchApiEvent(signupInternalRequest, MicroserviceType.Platform, Routes.SignupController.BasePath, Routes.SignupController.Internal.SIGNUP_PROCESS, requeueDelay))
                return Ok();
            return StatusCode(500, "requeue error");
        }


        [HttpGet(Routes.SignupController.Public.CONFIRM_EMAIL)]
        public async Task<IActionResult> ConfirmEmail([FromQuery] EmailConfirmationRequest emailConfirmationRequest)
        {
            var signupEntry = await GetDBEntry<SignupProcess>(emailConfirmationRequest.SignupId);
            if (signupEntry == null) return BadRequest("This account does not exist.");

            if (signupEntry.Email != emailConfirmationRequest.Email) return BadRequest("Something went wrong with your confirmation");

            bool confirmed = await cognito.ConfirmEmail(emailConfirmationRequest.Email, emailConfirmationRequest.Code);

            if (confirmed) {
                signupEntry.Status = Models.Enum.SignupStatus.FINISHED;

                SignupInternalRequest confirmationReq = new SignupInternalRequest();
                confirmationReq.SignupProcess = signupEntry;

                await DispatchApiEvent(confirmationReq, MicroserviceType.Platform, Routes.SignupController.BasePath, Routes.SignupController.Internal.SIGNUP_PROCESS);
                return Ok();
            }
            return BadRequest("Your code is invalid or expired.");
        }



        [HttpGet(Routes.SignupController.Public.SIGNUP_STATUS)]
        public async Task<IActionResult> SignupStatus(string requestId)
        {
            SignupProcess signup = await GetDBEntry<SignupProcess>(requestId);

            SignupResponse signupResponse = new SignupResponse();
            signupResponse.Status = signup.Status;
            signupResponse.RequestId = signup.Id;

            return Ok(signupResponse);
        }


        [HttpPost(Routes.SignupController.Public.RESEND_CONFIRMATION_EMAIL)]
        public async Task<IActionResult> ResendConfirmationCode(string email)
        {
            if (await cognito.ResendConfirmationEmail(email))
            {
                return Ok();
            }
            return BadRequest("Email not found");
        }

    }
}
