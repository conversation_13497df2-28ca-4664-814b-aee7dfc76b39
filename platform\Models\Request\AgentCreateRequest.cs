﻿using platform.Models.Enum;
using shared.Converters;
using System.Text.Json.Serialization;

namespace platform.Models.Request
{
    public class AgentCreateRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Instructions { get; set; } = string.Empty;
        [JsonConverter(typeof(JsonEnumStringConverter<AgentType>))]
        public AgentType AgentModel { get; set; } = AgentType.Claude_v2;
    }
}
