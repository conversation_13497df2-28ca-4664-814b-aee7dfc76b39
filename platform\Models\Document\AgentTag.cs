﻿using Amazon.DynamoDBv2.DataModel;
using shared.Models.Document;

namespace platform.Models.Document
{
    public class DeployedKnowledgeBaseInfo
    {
        public string KbId { get; set; } = string.Empty;
        public string KbName { get; set; } = string.Empty;
        public string KbTag { get; set; } = string.Empty;
    }

    [DynamoDBTable(nameof(AgentTag))]
    public class AgentTag : BaseModel
    {
        [DynamoDBHashKey]
        public string AgentId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Tag { get; set; } = string.Empty;
        public string AccountId { get; set; } = string.Empty;
    }
}
