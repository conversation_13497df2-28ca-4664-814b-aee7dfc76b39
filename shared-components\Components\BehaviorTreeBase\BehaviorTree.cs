﻿
using AutoMapper;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTreeBase;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Models.Document;
using shared.Components.Flow;
using shared.Models.Document;
using System.Text.Json.Serialization;

namespace shared.Components.BehaviorTree
{
    public class BehaviorTree<TState, TEvalResult> where TState : TreeState, new() where TEvalResult : BehaviorTreeEvaluationResult, new()
    {
        public string TreeId { get; private set; } = string.Empty;
        public Node.Node? TreeTemplate { get; private set; } = null;
        public TState TreeState { get; set; } = new TState();


        public BehaviorTree(Node.Node root, TState treeState)
        {
            this.TreeTemplate = root;
            this.TreeState = treeState;
            this.TreeId = Guid.NewGuid().ToString();
        }

        public BehaviorTree()
        {
            this.TreeTemplate = null;
            this.TreeState = new TState();
            this.TreeId = string.Empty;
        }

        public async virtual Task<TEvalResult> Evaluate()
        {
            TEvalResult res = new TEvalResult();
            if (TreeTemplate == null) {
                res.TreeNodesState = BehaviorTreeBase.Enum.NodeState.FAILURE;
                return res;
            }
            res.TreeNodesState = await TreeTemplate.Evaluate(TreeState);
            return res;
        }

    }
}
