﻿using Amazon.CognitoIdentityProvider;
using Amazon.DynamoDBv2.DataModel;
using Amazon.IdentityManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Components.AWS;
using platform.Configuration;
using platform.Constants;
using platform.Models.Document;
using platform.Models.Request;
using platform.Models.Response;
using shared.Authentication;
using shared.Components.ApiEventBus;
using shared.Models.Configuration;
using shared.Services;
using System.Security.Claims;

namespace platform.Controllers
{
    [ApiController]
    [Route(Routes.SigninController.BasePath)]
    public class SigninController : shared.Controllers.SuperController
    {
        private readonly IAmazonCognitoIdentityProvider identityProvider;
        private readonly Cognito cognito;
        private readonly ISecretsService secretsService;
        private readonly IOptions<MicroserviceConfiguration> microserviceConfiguration;
        private readonly IOptionsMonitor<UserPoolConfiguration> configuration;

        public SigninController(IApiEventBus eventBus, IDynamoDBContext dBContext, IAmazonCognitoIdentityProvider identityProvider, ISecretsService secretsService, IOptionsMonitor<UserPoolConfiguration> configuration, IOptions<MicroserviceConfiguration> microserviceConfiguration) : base(eventBus, dBContext)
        {
            this.identityProvider = identityProvider;
            this.secretsService = secretsService;
            this.microserviceConfiguration = microserviceConfiguration;
            this.configuration = configuration;

            cognito = new Cognito(identityProvider, microserviceConfiguration, configuration, secretsService);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.SigninController.Public.UPDATE_TOKEN)]
        public async Task<IActionResult> UpdateToken()
        {
            UpdateJwtResponse resp = new UpdateJwtResponse() { Jwt = await JWTHelper.GenerateJwtUser(secretsService, microserviceConfiguration.Value, GetClaims(), configuration.CurrentValue.LoginExpireTimeout) };
            return Ok(resp);
        }

        [HttpPost]
        public async Task<IActionResult> SignIn(SigninRequest request)
        {

            var resp = await cognito.Login(request.Email, request.Password);

            if (resp.Status != Models.Enum.SigninStatus.LOGIN_SUCCESSFUL)
            {
                return Ok(resp);
            }

            var accountUser = await GetDBEntry<AccountUser>(resp.AccountId, resp.UserId);
            if(accountUser == null)
            {
                var search = dBContext.QueryAsync<AccountUser>(resp.UserId, new DynamoDBOperationConfig { IndexName = AccountUser.SECONDARY_INDEX_NAME });
                List<AccountUser> accounts = await search.GetNextSetAsync();
                accountUser = accounts.First();

                resp.AccountId = accountUser.AccountId;

                await cognito.UpdateUserAttributes(request.Email, new Dictionary<string, string>() { { "custom:AccountId", accountUser.AccountId } });
            }

            List<Claim> claims = new List<Claim>()
            {
                new Claim("AccountId", accountUser.AccountId),
                new Claim("UserId", accountUser.UserId),
                new Claim("Level", accountUser.Role.ToString())
            };

            resp.Jwt = await JWTHelper.GenerateJwtUser(secretsService, microserviceConfiguration.Value, claims, configuration.CurrentValue.LoginExpireTimeout);
            return Ok(resp);
        }
    }

    


}
