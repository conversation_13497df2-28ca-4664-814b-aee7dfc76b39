﻿using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Models.Document;
using shared.Converters;
using System.Text.Json.Serialization;

namespace platform.Models.Document
{

    [DynamoDBTable("AccountUser")]
    public class AccountUser : BaseModel
    {

        public const string SECONDARY_INDEX_NAME = "UserIndex"; 

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexHashKey]
        public string UserId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<UserRole>))]
        [JsonConverter(typeof(JsonEnumStringConverter<UserRole>))]
        public UserRole Role { get; set; } = UserRole.Standard;        
        public long LastAccess { get; set; } = 0;

    }
}
