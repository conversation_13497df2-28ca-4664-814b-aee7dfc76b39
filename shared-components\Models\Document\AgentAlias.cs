﻿using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Document
{
    [DynamoDBTable(nameof(AgentAlias))]
    public class AgentAlias : BaseModel
    {
        [DynamoDBHashKey]
        public string AgentId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Alias { get; set; } = string.Empty;
        public string AccountId {  get; set; } = string.Empty;
        public string AgentTag { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
