﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime.Internal;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using shared.Models.Document;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace shared.Authentication
{
    public class ApiKeyAuthenticationHandler<T> : AuthenticationHandler<ApiKeyAuthenticationOptions> where T : APIKeyStorageBaseModel
    {

        private readonly ILogger<ApiKeyAuthenticationHandler<T>> logger;
        private readonly IDynamoDBContext dbContext;

        public ApiKeyAuthenticationHandler(IOptionsMonitor<ApiKeyAuthenticationOptions> options, ILogger<ApiKeyAuthenticationHandler<T>> logger, UrlEncoder encoder, ILoggerFactory loggerFactory, IDynamoDBContext dBContext) : base(options, loggerFactory, encoder)
        {
            this.logger = logger;
            this.dbContext = dBContext;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var endpoint = Context.GetEndpoint();
            if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null)
                return await Task.FromResult(AuthenticateResult.NoResult());

            string key = string.Empty;
            string secret = string.Empty;

            if (Request.Query.ContainsKey("key") && Request.Query.ContainsKey("secret"))
            {
                key = Request.Query["key"].ToString();
                secret = Request.Query["secret"].ToString();
            }
            else
            {
                return await Task.FromResult(AuthenticateResult.Fail("Authentication failed: No API key or secret provided"));
            }

            T? keyObj = await dbContext.LoadAsync<T>(key, secret);
            if (keyObj == null) return await Task.FromResult(AuthenticateResult.Fail("Authentication failed: API invalid or expired."));

            if(keyObj.ExpireAt >= 0 && keyObj.ExpireAt < new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds())
            {
                await dbContext.DeleteAsync(key);
                return await Task.FromResult(AuthenticateResult.Fail("Authentication failed: API key expired."));
            }
                

            var identity = new ClaimsIdentity(keyObj.Claims, Scheme.Name);
            identity.AddClaim(new Claim("AccountId", keyObj.AccountId));

            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return await Task.FromResult(AuthenticateResult.Success(ticket));

        }
    }
}
