﻿using Amazon.DynamoDBv2.DataModel;
using Microsoft.VisualBasic;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTreeBase.Models;
using shared.Converters;
using shared.Models.Document;
using System.Text.Json.Serialization;

namespace coral_agents.Models.Document
{
    [DynamoDBTable("Session")]
    public class Session : BaseModel
    {
        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string SessionId { get; set; } = string.Empty;
        public string TreeId { get; set; } = string.Empty;
        public AgentTreeState TreeState { get; set; } = new AgentTreeState();
        public object? ProviderData { get; set; }
    }
}
