﻿using shared.Components.APIEvents;
using System.Security.Claims;
using System.Text.Json;
using System.Text;
using shared.Models.Configuration;
using Amazon.SQS;
using shared.Models.Enum;
using shared.Extensions;
using Amazon.SQS.Model;
using shared.Authentication;
using shared.Services;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting;
using shared.Helpers;

namespace shared.Components.ApiEventBus.Implementation
{
    public class ProcessMessageResult
    {
        public bool Success { get; set; }
    }

    public class AWSApiEventBus : IApiEventBus
    {
        private readonly ILogger<AWSApiEventBus> logger;
        private readonly IAmazonSQS amazonSQS;
        private readonly ISecretsService secretsService;
        private readonly CancellationTokenSource cts = new CancellationTokenSource();
        private readonly HttpClient httpClient = new HttpClient();
        private readonly MicroserviceConfiguration microserviceConfiguration;
        private readonly IServer server;
        private readonly IHostApplicationLifetime lifetime;

        private Uri? baseUri = null;

        private string MakeQueueEndpoint(MicroserviceType microServiceType)
        {
            return $"eventbus-{microserviceConfiguration.Group}-{microServiceType.DisplayName()}";
        }

        public AWSApiEventBus(ILogger<AWSApiEventBus> logger, IAmazonSQS amazonSQS, IOptions<MicroserviceConfiguration> microserviceConfigurationOpts, ISecretsService secretsService, IServer server, IHostApplicationLifetime lifetime)
        {
            this.logger = logger;
            this.amazonSQS = amazonSQS;
            this.secretsService = secretsService;
            microserviceConfiguration = microserviceConfigurationOpts.Value;
            this.server = server;
            this.lifetime = lifetime;
        }

        public async Task<bool> Send(object payload, MicroserviceType target, string controller, string route, List<Claim>? claims = null, int delayInSeconds = 0)
        {
            ApiEventBusMessage msg = new ApiEventBusMessage();
            msg.ApiEvent = new ApiEvent();
            msg.ApiEvent.Payload = payload;
            msg.ApiEvent.Endpoint = controller + "/" + route;
            msg.ApiEvent.Method = HttpMethod.Post;

            msg.Source = microserviceConfiguration.FullId;
            msg.DelayInSeconds = delayInSeconds;
            msg.Claims = claims ?? new List<Claim>();
            msg.TargetMicroservice = target;

            return await Send(msg);
        }

        private async Task<bool> Send(ApiEventBusMessage message)
        {

            string _queueName = MakeQueueEndpoint(message.TargetMicroservice);

            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            sendMessageRequest.DelaySeconds = message.DelayInSeconds;
            sendMessageRequest.MessageBody = JsonSerializer.Serialize(message);
            sendMessageRequest.QueueUrl = _queueName;
            SendMessageResponse responseSendMsg = await amazonSQS.SendMessageAsync(sendMessageRequest);
            return (responseSendMsg != null && responseSendMsg.MessageId != null);
        }

        private async Task<string> MakeToken(List<Claim>? additionalClaims)
        {
            List<Claim> claims = new List<Claim>() { 
                new Claim("ms", microserviceConfiguration.FullId)
            };
            
            if (additionalClaims != null) claims.AddRange(additionalClaims);
            
            return await JWTHelper.GenerateJwtMicroService(secretsService, microserviceConfiguration, claims);
        }

        private async Task<bool> RunConsumer(CancellationToken cancellationToken)
        {
            if (!await StartupHelper.WaitForAppStartup(lifetime, cancellationToken))
            {
                return false;
            }

            var features = server.Features.Get<IServerAddressesFeature>();
            if (features == null) throw new Exception("Couldn't determ application base URL");
            baseUri = new Uri(features.Addresses.First());

            string selfEndpoint = MakeQueueEndpoint(microserviceConfiguration.Type);
            do
            {
                var msg = await GetMessage(amazonSQS, selfEndpoint);
                if (msg.Messages.Count != 0)
                {
                    ProcessMessageResult processResult = await ProcessMessage(msg.Messages[0]);
                    if (processResult.Success) await DeleteMessage(amazonSQS, msg.Messages[0], selfEndpoint);
                }
            } while (!cts.IsCancellationRequested);
            return true;
        }

        private async Task<ReceiveMessageResponse> GetMessage(IAmazonSQS sqsClient, string queueEndpoint)
        {
            return await sqsClient.ReceiveMessageAsync(new ReceiveMessageRequest
            {
                QueueUrl = queueEndpoint,
                MaxNumberOfMessages = 1,
                WaitTimeSeconds = 20,
                VisibilityTimeout = 120
            });
        }

        private async Task<ProcessMessageResult> ProcessMessage(Message message)
        {

            if (baseUri == null) throw new ArgumentNullException("baseUri is null and shouldn~t be at this point");

            ApiEventBusMessage? msg = JsonSerializer.Deserialize<ApiEventBusMessage>(message.Body);
            if (msg == null)
            {
                return new ProcessMessageResult();
            }

            string bearer = await MakeToken(msg.Claims);

            HttpRequestMessage request = new HttpRequestMessage();
            request.Method = msg.ApiEvent.Method;
            request.RequestUri = new Uri(baseUri, msg.ApiEvent.Endpoint);
            request.Headers.Add("Authorization", $"Bearer {bearer}");
            request.Content = new StringContent(JsonSerializer.Serialize(msg.ApiEvent.Payload), Encoding.UTF8, "application/json");

            logger.LogInformation($"Event calling {request.RequestUri.ToString()}");

            HttpResponseMessage result = await httpClient.SendAsync(request);
            return new ProcessMessageResult { Success = result.IsSuccessStatusCode };
        }

        private async Task DeleteMessage(IAmazonSQS sqsClient, Message message, string queueEndpoint)
        {
            await sqsClient.DeleteMessageAsync(queueEndpoint, message.ReceiptHandle);
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            Task.Run(() => RunConsumer(cancellationToken));
            return Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            await cts.CancelAsync();
        }
    }
}
