﻿using Amazon.DynamoDBv2.DataModel;

namespace shared.Models.Document
{
    [DynamoDBTable("Flow")]
    public class Flow
    {
        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Nodes { get; set; } = string.Empty;
        public string Connections { get; set; } = string.Empty;

    }
}
