using Amazon.BedrockAgentRuntime;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.SQS;
using Microsoft.IdentityModel.Tokens;
using shared.Authentication;
using shared.Components.ApiEventBus;
using shared.Components.ApiEventBus.Implementation;
using shared.Models.Configuration;
using shared.Models.Document;
using shared.Services.Implementation;
using shared.Services;
using Amazon.SecretsManager;
using shared.Constants;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddLogging(loggingBuilder => loggingBuilder
            .AddConsole()
            .AddDebug()
            .SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug));
builder.Services.AddDefaultAWSOptions(builder.Configuration.GetAWSOptions());

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.Configure<MicroserviceConfiguration>(builder.Configuration.GetSection(nameof(MicroserviceConfiguration)));

builder.Services.AddAWSService<IAmazonDynamoDB>();
builder.Services.AddAWSService<IAmazonSQS>();
builder.Services.AddAWSService<IAmazonBedrockAgentRuntime>();
builder.Services.AddSingleton<IDynamoDBContext, DynamoDBContext>();
builder.Services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
builder.Services.AddSingleton<IAmazonBedrockAgentRuntime, AmazonBedrockAgentRuntimeClient>();
builder.Services.AddSingleton<IAmazonSecretsManager, AmazonSecretsManagerClient>();
builder.Services.AddSingleton<ISecretsService, AWSSecretsService>();

builder.Services.AddAuthentication()
    .AddScheme<ApiKeyAuthenticationOptions, ApiKeyAuthenticationHandler<APIKey>>(Authentication.APIKeyAuthScheme, options => { })
    .AddScheme<SecretsJwtBearerOptions, SecretsJwtBearerHandler>(
        shared.Constants.Authentication.AgentQueryAuthScheme,
        x =>
        {
            x.SecretsKeyName = shared.Constants.Authentication.AgentAuthenticationKeySecretsName;
            x.RequireHttpsMetadata = false;
            x.SaveToken = false;
            x.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };
        });
builder.Services.AddSingleton<IApiEventBus, AWSApiEventBus>();
builder.Services.AddHostedService<IApiEventBus>(provider => provider.GetRequiredService<IApiEventBus>());

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
