using Amazon.DynamoDBv2.DataModel;
using coral_agents.Models.Document;
using coral_agents.Models.Request;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;
using shared.Components.ApiEventBus;
using shared.Components.BehaviorTree;
using shared.Constants;
using shared.Controllers;
using shared.Models.Document;
using coral_agents.Constants;
using shared.Authentication;
using System.Security.Claims;
using shared.Services;
using Microsoft.Extensions.Options;
using shared.Models.Configuration;
using Amazon.Runtime.Internal;
using coral_agents.Components.AWS;
using Amazon.BedrockAgentRuntime;
using coral_agents.Models.Document.AWS;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.AgentBehaviorTree;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Models.Document.AWS;
using shared.Components.BehaviorTreeBase;
using System.Text.Json;
using shared.Extension;
using Amazon.BedrockAgentRuntime.Model;

namespace coral_agents.Controllers
{

    [Route(Routes.SessionController.BasePath)]
    [Produces("application/json")]
    public class SessionController : SuperController
    {
        private readonly ILogger<SessionController> logger;
        private readonly ISecretsService secretsService;
        private readonly IOptions<MicroserviceConfiguration> options;
        private readonly IAmazonBedrockAgentRuntime amazonBedrockAgent;

        public SessionController(ILogger<SessionController> logger, IApiEventBus eventBus, IDynamoDBContext dbContext, ISecretsService secretsService, IAmazonBedrockAgentRuntime amazonBedrockAgent, IOptions<MicroserviceConfiguration> options) : base(eventBus, dbContext)
        {
            this.logger = logger;
            this.secretsService = secretsService;
            this.options = options;
            this.amazonBedrockAgent = amazonBedrockAgent;
        }


        [Authorize(AuthenticationSchemes = Authentication.AgentQueryAuthScheme)]
        [HttpPost(Routes.SessionController.Public.END_SESSION)]
        public async Task<IActionResult> EndSession()
        {
            string accountId = GetAccountId();
            string sessionId = GetClaimValue("SessionId") ?? string.Empty;

            AwsSession session = await GetDBEntry<AwsSession>(accountId, sessionId);
            if (session == null) return BadRequest("Session does not exist or is expired.");

            AgentBehaviorTreeDocument? tree = await GetDBEntry<AgentBehaviorTreeDocument>(accountId, session.TreeId);
            if (tree == null) return this.BadRequest("TreeId Doesn't exist.");


            string awsAgentId;
            string awsAgentAlias;
            string awsAgentAliasId;

            if (session.TreeState.Result.AgentReference.Value.IsAlias)
            {
                AwsAgentAlias agentAlias = await GetDBEntry<AwsAgentAlias>(session.TreeState.Result.AgentReference.Value.AgentId, session.TreeState.Result.AgentReference.Value.AliasOrTag);
                if (agentAlias == null) return BadRequest("Alias not found");
                awsAgentId = agentAlias.AwsData.AgentId;
                awsAgentAlias = agentAlias.AwsData.AliasName;
                awsAgentAliasId = agentAlias.AwsData.AliasId;
            }
            else
            {
                AwsAgentTag agentTag = await GetDBEntry<AwsAgentTag>(accountId, session.TreeState.Result.AgentReference.Value.AliasOrTag);
                if (agentTag == null) return BadRequest("Tag not found");
                awsAgentId = agentTag.AwsData.AgentId;
                awsAgentAlias = agentTag.AwsData.AliasName;
                awsAgentAliasId = agentTag.AwsData.AliasId;
            }


            Bedrock bedrock = new Bedrock(amazonBedrockAgent);
            var response = await bedrock.InvokeAgent(
                "",
                awsAgentId,
                awsAgentAlias,
                awsAgentAliasId,
                session.SessionId,
                true
            );

            return Ok(response);
        }


        [Authorize(AuthenticationSchemes = Authentication.AgentQueryAuthScheme)]
        [HttpPost(Routes.SessionController.Public.QUERY)]
        public async Task<IActionResult> AgentMessage([FromBody] SessionMessageRequest sessionMessageRequest)
        {
            string accountId = GetAccountId();
            string sessionId = GetClaimValue("SessionId") ?? string.Empty;

            AwsSession session = await GetDBEntry<AwsSession>(accountId, sessionId);
            if (session == null) return BadRequest("Session does not exist or is expired.");

            AgentBehaviorTreeDocument? tree = await GetDBEntry<AgentBehaviorTreeDocument>(accountId, session.TreeId);
            if (tree == null) return this.BadRequest("TreeId Doesn't exist.");


            AgentBehaviorTree bt = BehaviorTreeBuilder<AgentBehaviorTree, JsonAgentBehaviorTree>.MapToObject(tree.TreeDefinition);
            bt.TreeState = session.TreeState;
            bt.TreeState.UserInput = sessionMessageRequest.UserInput;
            AgentTreeEvaluationResult res = await bt.Evaluate();

            res.Prompt = res.Prompt.FormatFromDictionary(new Dictionary<string, string>() { { "userInput", sessionMessageRequest.UserInput } }, "#");

            if (!res.IsFinished())
            {
                return Ok("Processing not finished");
                //TODO
            }

            string awsAgentId;
            string awsAgentAlias;
            string awsAgentAliasId;

            if (res.Agent.IsAlias)
            {
                AwsAgentAlias agentAlias = await GetDBEntry<AwsAgentAlias>(res.Agent.AgentId, res.Agent.AliasOrTag);
                if(agentAlias == null) return BadRequest("Alias not found");
                awsAgentId = agentAlias.AwsData.AgentId;
                awsAgentAlias = agentAlias.AwsData.AliasName;
                awsAgentAliasId = agentAlias.AwsData.AliasId;
            }
            else
            {
                AwsAgentTag agentTag = await GetDBEntry<AwsAgentTag>(accountId, res.Agent.AliasOrTag);
                if (agentTag == null) return BadRequest("Tag not found");
                awsAgentId = agentTag.AwsData.AgentId;
                awsAgentAlias = agentTag.AwsData.AliasName;
                awsAgentAliasId = agentTag.AwsData.AliasId;
            }



            Bedrock bedrock = new Bedrock(amazonBedrockAgent);
            var response = await bedrock.InvokeAgent(
                sessionMessageRequest.UserInput,
                awsAgentId,
                awsAgentAlias,
                awsAgentAliasId,
                session.SessionId,
                false
            );
            
            return Ok(response);
        }


        [Authorize(AuthenticationSchemes = Authentication.APIKeyAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] SessionCreateRequest request)
        {

            if (request == null) return BadRequest("Invalid JSON schema, please check the API documentation for this endpoint.");

            string? accountId = this.GetAccountId();
            if (accountId == null) return BadRequest("Your account is Broken.");

            AgentBehaviorTreeDocument? tree = await GetDBEntry<AgentBehaviorTreeDocument>(accountId, request.TreeId);
            if (tree == null) return this.BadRequest("TreeId Doesn't exist.");

            var treeState = new AgentTreeState();

            foreach (KeyValuePair<string, BehaviorTreeInput> entry in tree.TreeDefinition.BehaviorTreeInputs)
            {
                JsonElement value;
                if (!request.Inputs.TryGetValue(entry.Key, out value) && entry.Value.Required)
                {
                    return this.BadRequest($"Mandatory input {entry.Key} is missing.");
                }

                if (value.ValueKind != BehaviorTreeInput.BehaviorTreeInputTypeToJsonElementType[entry.Value.InputType])
                {
                    return this.BadRequest($"Input {entry.Key} is {value.ValueKind}, but {entry.Value.InputType.ToString()} was expected.");
                }                    

                treeState.Inputs.Add(entry.Key, value.ToString());
            }

            Session newSession = new Session();
            newSession.SessionId = Guid.NewGuid().ToString();
            newSession.TreeId = request.TreeId;
            newSession.AccountId = accountId;
            newSession.TreeState = treeState;

            await PutDBEntry(newSession);

            var resp = new SessionCreateResponse();
            resp.Session = newSession;
            resp.JWT = await JWTHelper.GenerateJwtAgent(secretsService, options.Value, new List<Claim>() {
                new Claim("AccountId", accountId),
                new Claim("SessionId", newSession.SessionId)
            }, 3600*24*30);

            return Ok(resp);
        }
    }
}