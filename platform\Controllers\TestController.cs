﻿using Amazon.DynamoDBv2.DataModel;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using platform.Constants;
using platform.Models.Request;
using platform.Services;
using shared.Components.AgentBehaviorTree;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.AgentBehaviorTree.Tasks;
using shared.Components.ApiEventBus;
using shared.Components.BehaviorTree;
using shared.Components.BehaviorTree.Condition;
using shared.Components.BehaviorTree.Node;
using shared.Components.BehaviorTreeBase;
using shared.Components.BehaviorTreeBase.Condition.InputCondition;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Models.Document;
using shared.Components.BehaviorTreeBase.Tasks;
using shared.Controllers;
using shared.Extension;
using shared.Models.Document;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace platform.Controllers
{
    [ApiController]
    [Route("test")]
    public class TestController : SuperController
    {
        private readonly IIAM iIAM;

        public TestController(IApiEventBus eventBus, IDynamoDBContext dBContext, IIAM iIAM) : base(eventBus, dBContext)
        {
            this.iIAM = iIAM;
        }

        [HttpGet]
        public async Task<IActionResult> Get() {
            string accNum = await iIAM.GetProviderAccountNumber();
            return Ok(accNum);
        }

        [HttpGet("1")]
        public async Task<IActionResult> TreeTest1()
        {
            BehaviorTreeInput behviorTreeInput = new BehaviorTreeInput();
            behviorTreeInput.Required = true;
            behviorTreeInput.InputType = shared.Components.BehaviorTreeBase.Enum.BehaviorTreeInputType.STRING;


            JsonAgentBehaviorTree bt = new JsonAgentBehaviorTree()
            {
                DefaultAgent = new AgentReference() { AgentId = "938ee8ac-6006-4fcc-a6d0-86a7b6e70e6f", AliasOrTag = "v0.4", IsAlias = false },
                PromptTemplate = "prompt is: {t1}!!",
                TreeId = "1",
                BehaviorTreeInputs = new Dictionary<string, BehaviorTreeInput>() {
                    {
                        "input1",
                        new BehaviorTreeInput() { InputType = shared.Components.BehaviorTreeBase.Enum.BehaviorTreeInputType.STRING, Required = true }
                    }
                },
                TreeTemplate = new Sequence("sequence", new List<Node>()
                {
                    new SetPromptTask("promptNode", new List<Node>(), null, new shared.Types.PromptEntry() {
                        Prompt = new shared.Types.PriorityValue<string>(){ Priority=1, Value = "test"},
                        PromptKey = "t1"
                    }),
                    new SetAgentTask("promptNode", new List<Node>(), null,
                    new shared.Types.PriorityValue<AgentReference>(){ Priority=1, Value = new AgentReference() {
                        AgentId="938ee8ac-6006-4fcc-a6d0-86a7b6e70e6f", AliasOrTag = "prod", IsAlias = true
                    } })
                }, new ConditionExpression()
                {
                    Operation = ConditionOperation.AND,
                    Not = false,
                    Conditions = new List<ICondition>() {
                            new StringInputCondition(){ InputKey = "input1", Value = "value1", Comparator = shared.Components.BehaviorTreeBase.Enum.ComparatorType.Equals }
                    }
                })
            };

            var btobj = BehaviorTreeBuilder<AgentBehaviorTree, JsonAgentBehaviorTree>.MapToObject(bt);
            btobj.TreeState = new AgentTreeState() { Inputs = new Dictionary<string, string>() { {"input1", "value1"} } };

            var res = await btobj.Evaluate();

            return Ok(JsonSerializer.Serialize(bt));
        }

        [HttpGet("2")]
        public async Task<IActionResult> TreeTest2()
        {
            
            JsonAgentBehaviorTree bt = new JsonAgentBehaviorTree()
            {
                DefaultAgent = new AgentReference() { AgentId = "938ee8ac-6006-4fcc-a6d0-86a7b6e70e6f", AliasOrTag = "v0.4", IsAlias = false },
                PromptTemplate = "prompt is: {t1}!!",
                TreeId = "1",
                BehaviorTreeInputs = new Dictionary<string, BehaviorTreeInput>() {
                    {
                        "input1",
                        new BehaviorTreeInput() { InputType = shared.Components.BehaviorTreeBase.Enum.BehaviorTreeInputType.STRING, Required = true }
                    }
                },
                TreeTemplate = new Sequence("sequence", new List<Node>()
                {
                    new SetPromptTask("promptNode", new List<Node>(), null, new shared.Types.PromptEntry() {
                        Prompt = new shared.Types.PriorityValue<string>(){ Priority=1, Value = "test"},
                        PromptKey = "t1"
                    }),
                    new SetAgentTask("promptNode", new List<Node>(), null,
                    new shared.Types.PriorityValue<AgentReference>(){ Priority=1, Value = new AgentReference() {
                        AgentId="938ee8ac-6006-4fcc-a6d0-86a7b6e70e6f", AliasOrTag = "prod", IsAlias = true
                    } })
                }, new ConditionExpression()
                {
                    Operation = ConditionOperation.AND,
                    Not = false,
                    Conditions = new List<ICondition>() {
                            new StringInputCondition(){ InputKey = "input1", Value = "value1", Comparator = shared.Components.BehaviorTreeBase.Enum.ComparatorType.Equals }
                    }
                })
            };

            AgentBehaviorTreeCreateRequest request = new AgentBehaviorTreeCreateRequest() { Name = "test2" }; //, TreeDefinition = bt };
            AgentBehaviorTreeDocument behaviorTreeData = new AgentBehaviorTreeDocument();
            //behaviorTreeData.TreeDefinition = request.TreeDefinition;
            behaviorTreeData.Name = request.Name;
            behaviorTreeData.Id = Guid.NewGuid().ToString();
            behaviorTreeData.AccountId = "test";

            await PutDBEntry(behaviorTreeData);

            return Ok(bt);

        }

        [HttpGet("3")]
        public async Task<IActionResult> TreeTest3()
        {
            string template = "Hi, this is prompt1: {{!prompt1}}. And this is prompt2: {{!prompt2}}. In template: {{#ts}}. User: {{#userInput}}.";
            Dictionary<string, string> promtps = new Dictionary<string, string>() { { "prompt1", " input1 tem valor: {{%input1}}, input2 tem valor: {{%input2}}" }, { "prompt2", "var1 tem valor: {{@var1}}, o ts agora é: {{#ts}} e o input do user é: {{#userInput}}" } };
            Dictionary<string, string> inputs = new Dictionary<string, string>() { { "input1", "val_input1" } };
            Dictionary<string, string> variables = new Dictionary<string, string>() { { "var1", "val_var1" }, { "var2", "val_var2" } };
            Dictionary<string, string> userinput = new Dictionary<string, string>() { { "userInput", "Essa foi a entrada do usuario" } };
            Dictionary<string, Func<string>> functions = new Dictionary<string, Func<string>>() { { "ts", new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString } };

            string res = template.FormatFromDictionary(promtps, "!");
            res = res.FormatFromDictionary(inputs, "%");
            res = res.FormatFromDictionary(variables, "@");
            res = res.FormatFromDictionary(userinput, "#", cleanMissing: false);
            res = res.FormatFromDictionary(functions, "#");

            return Ok(res);


        }
    }
}
