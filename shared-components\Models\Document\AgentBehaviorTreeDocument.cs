using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Models.Document;
using shared.Components.BehaviorTree;
using shared.Components.BehaviorTreeBase.Models;
using shared.Components.BehaviorTreeBase.Models.Document;

namespace shared.Models.Document
{
    [DynamoDBTable("AgentBehaviorTree")]
    public class AgentBehaviorTreeDocument : BaseModel
    {
        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonAgentBehaviorTree TreeDefinition { get; set; } = new JsonAgentBehaviorTree();
    }
}
