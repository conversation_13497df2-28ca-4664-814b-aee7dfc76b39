﻿using Amazon.DynamoDBv2.DataModel;
using shared.Components.AgentBehaviorTree.Models.Document;

namespace shared.Models.Document
{
    [DynamoDBTable("AgentBehaviorTree")]
    public class AgentBehaviorTreeDocument : BaseModel
    {
        public const string AccountIdIdIndex = "AccountId-Id-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdIdIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string AgentId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdIdIndex)]
        public string Id { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonAgentBehaviorTree TreeDefinition { get; set; } = new JsonAgentBehaviorTree();
    }
}
