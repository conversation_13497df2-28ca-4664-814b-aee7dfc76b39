<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>e2118689-7c05-48e1-8443-bfe8eb74cf45</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="2.5.5" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AWSSDK.BedrockAgent" Version="3.7.414" />
    <PackageReference Include="AWSSDK.CognitoIdentityProvider" Version="3.7.404.2" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.400.59" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.301" />
    <PackageReference Include="AWSSDK.IdentityManagement" Version="3.7.403.8" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.410.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\shared-components\shared.csproj" />
  </ItemGroup>

</Project>
