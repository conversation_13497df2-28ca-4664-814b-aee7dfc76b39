﻿using platform.Models.Enum;
using shared.Converters;
using shared.Models.Enum;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class SigninResponse
    {
        public string SessionId { get; set; } = string.Empty;
        [JsonConverter(typeof(JsonEnumStringConverter<SigninStatus>))]
        public SigninStatus Status { get; set; } = SigninStatus.CREDENTIALS_NOT_FOUND;
        public string UserId { get; set; } = string.Empty;
        public string AccountId {  get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken {  get; set; } = string.Empty;
        public string Jwt { get; set; } = string.Empty;
        public int RefreshTimeout { get; set;} = -1;
    }
}
