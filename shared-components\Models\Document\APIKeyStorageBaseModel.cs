﻿using Amazon.DynamoDBv2.DataModel;
using shared.Converters;
using System.Security.Claims;
using System.Text.Json.Serialization;

namespace shared.Models.Document
{
    public class APIKeyStorageBaseModel : BaseModel
    {
        public const string AccountIdKeySecondaryGlobalIndex = "AccountId-Key-index";

        [DynamoDBHashKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdKeySecondaryGlobalIndex)]
        public string Key { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        public string Secret { get; set; } = string.Empty;
        [DynamoDBGlobalSecondaryIndexHashKey(AccountIdKeySecondaryGlobalIndex)]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoDBListOfConverter<Claim, DynamoDBClaimConverter>))]
        [JsonConverter(typeof(JsonListOfConverter<Claim, JsonClaimConverter>))]
        public List<Claim> Claims { get; set; } = new List<Claim>();
        public long ExpireAt { get; set; } = -1;
    }
}
