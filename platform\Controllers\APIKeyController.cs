﻿using Amazon.BedrockAgent;
using Amazon.DynamoDBv2.DataModel;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Constants;
using platform.Models.Configuration;
using platform.Models.Document.AWS;
using platform.Models.Request;
using platform.Models.Response;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Controllers;
using shared.Models.Document;
using shared.Models.Response;

namespace platform.Controllers
{
    [Route(Routes.APIKeyController.BasePath)]
    [Produces("application/json")]
    public class APIKeyController : SuperController
    {
        private readonly ILogger<AgentController> logger;

        public APIKeyController(ILogger<AgentController> logger, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext) : base(apiEventBus, dynamoDBContext)
        {
            this.logger = logger;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateApiKey([FromBody] ApiKeyCreateRequest request)
        {
            APIKey apiKey = new APIKey();

            apiKey.Key = Guid.NewGuid().ToString();
            apiKey.Secret = Guid.NewGuid().ToString();

            apiKey.ExpireAt = request.ExpireAt;
            if (apiKey.ExpireAt < new DateTimeOffset(DateTime.UtcNow).AddMinutes(30).ToUnixTimeSeconds())
            {
                apiKey.ExpireAt = new DateTimeOffset(DateTime.UtcNow).AddMinutes(30).ToUnixTimeSeconds();
            }

            if (apiKey.ExpireAt > new DateTimeOffset(DateTime.UtcNow).AddYears(1).ToUnixTimeSeconds())
            {
                apiKey.ExpireAt = new DateTimeOffset(DateTime.UtcNow).AddYears(1).ToUnixTimeSeconds();
            }

            if(request.PersonalKey)
                apiKey.OwnerUserId = GetClaimValue("UserId") ?? string.Empty;
            
            apiKey.AccountId = GetAccountId();
            apiKey.Name = request.Name;
            apiKey.Claims = new List<System.Security.Claims.Claim>();

            var result = await PutDBEntry(apiKey);

            if (result == null) return StatusCode(500, "Failed to create your API key");

            return Ok(result);

        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.APIKeyController.Public.LIST)]
        public async Task<IActionResult> ListKeys([FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;


            var listEntriesInternal = await GetDBEntries<APIKey>(
                nameof(AwsAgent.AccountId), 
                GetAccountId(), count, nextToken, getTotal: false, index: APIKey.AccountIdKeySecondaryGlobalIndex);

            listEntriesInternal.Entries.ForEach(entry => entry.Secret = string.Empty);

            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<APIKey, ApiKeyResponse>();
            }).CreateMapper();


            var result = new ListResponse<ApiKeyResponse>()
            {
                Entries = mapper.Map<IList<APIKey>, IList<ApiKeyResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }


    }
}
