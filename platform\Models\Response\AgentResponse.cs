using platform.Models.Enum;
using shared.Converters;
using shared.Models.Response;
using System.Text.Json.Serialization;

namespace platform.Models.Response
{
    public class AgentResponse : BaseModelResponse
    {
        public string AgentId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DefaultPrompt { get; set; } = string.Empty;
        [JsonConverter(typeof(JsonEnumStringConverter<AgentType>))]
        public AgentType LLMModelType { get; set; } = AgentType.Claude_v2;
        [JsonConverter(typeof(JsonEnumStringConverter<AgentStatus>))]
        public AgentStatus Status { get; set; } = AgentStatus.QUEUED;
    }
}