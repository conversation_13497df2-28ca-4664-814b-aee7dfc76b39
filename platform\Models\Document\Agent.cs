﻿using Amazon.DynamoDBv2.DataModel;
using platform.Models.Enum;
using shared.Converters;
using shared.Models.Document;
using System.Text.Json.Serialization;

namespace platform.Models.Document
{
    [DynamoDBTable("Agent")]
    public class Agent : BaseModel
    {
        [DynamoDB<PERSON>ash<PERSON>ey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string AgentId { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Instructions { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<AgentType>))]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentType>))]
        public AgentType AgentType { get; set; } = AgentType.Claude_v2;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<AgentStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentStatus>))]
        public AgentStatus Status { get; set; }  = AgentStatus.QUEUED;

        public override string GetSearchString()
        {
            return Name.ToLower();
        }
    }
}
